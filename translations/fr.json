{"casualGreeting": "hey", "configSystem": "Système de configuration", "configSystemExplainer": "Le message d'accueil affiché ci-dessous est piloté par le système de configuration. Pour modifier les propriétés de configuration, cliquez sur l'icône de clé dans la barre de navigation pour afficher le panneau Outils de mise en œuvre. Saisissez ensuite <2>modè<PERSON></2> dans l'entrée <4>Configuration de la recherche</4>. <PERSON><PERSON> devrait filtrer les propriétés de configuration pour afficher uniquement celles qui sont pertinentes pour ce module. Vous pouvez modifier les valeurs de ces propriétés et cliquer sur <6>Enregistrer</6> pour voir les modifications reflétées dans l'interface utilisateur", "connect": "Connecter", "connectExplainer": "Entrez en contact avec la communauté", "dataFetching": "Récupération de données", "designDocs": "Documents de conception", "designDocsExplainer": "Lisez la documentation de conception O3", "explainer": "Les exemples suivants illustrent certaines fonctionnalités clés du cadre O3", "extensionExplainer": "Voici quelques cases colorées. Parce qu'ils sont attachés en tant qu'extensions dans un emplacement, un administrateur peut modifier les cases affichées à l'aide de la configuration. Il se trouve que ces boîtes sont définies dans ce module, mais elles pourraient être attachées à cet emplacement même si elles se trouvaient dans un module différent", "extensionSystem": "Système d'extension", "formalGreeting": "bonjour", "frontendDocs": "Documentation frontale", "getPatient": "Obtenez un patient nommé", "getStarted": "Commencer", "getStartedExplainer": "<PERSON><PERSON>er une interface à partir de ce modèle", "learnExplainer": "Apprendre à utiliser le framework O3", "loading": "Chargement", "patientGetterExplainer": "Essayez de cliquer sur le bouton ci-dessous pour récupérer un patient depuis le backend", "resources": "Ressources", "usefulLinks": "Ci-dessous quelques liens vers des ressources utiles", "welcomeText": "Bienvenue dans l'application O3 Template"}