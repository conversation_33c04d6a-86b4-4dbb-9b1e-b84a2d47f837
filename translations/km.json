{"casualGreeting": "hey", "configSystem": "ប្រព័ន្ធកំណត់រចនាសម្ព័ន្ធ", "configSystemExplainer": "ការស្វាគមន៍ដែលបង្ហាញខាងក្រោមត្រូវបានជំរុញដោយប្រព័ន្ធកំណត់រចនាសម្ព័ន្ធ។ ដើម្បីផ្លាស់ប្តូរលក្ខណសម្បត្តិនៃការកំណត់រចនាសម្ព័ន្ធ សូមចុចរូបតំណាង spanner នៅក្នុងរបាររុករក ដើម្បីទាញបន្ទះឧបករណ៍អនុវត្ត។ បន្ទាប់មក វាយ <2>ពុម្ព</2> ទៅក្នុងធាតុបញ្ចូល <4>ការស្វែងរក</4>។ វាគួរត្រងលក្ខណសម្បត្តិនៃការកំណត់រចនាសម្ព័ន្ធ ដើម្បីបង្ហាញតែអ្វីដែលពាក់ព័ន្ធទៅនឹងម៉ូឌុលនេះប៉ុណ្ណោះ។ អ្នកអាចផ្លាស់ប្តូរតម្លៃនៃលក្ខណៈសម្បត្តិទាំងនេះ ហើយចុច <6>រក្សាទុក</6> ដើម្បីមើលការផ្លាស់ប្តូរដែលឆ្លុះបញ្ចាំងនៅក្នុង UI", "connect": "ភ្ជាប់", "connectExplainer": "ទាក់ទងជាមួយសហគមន៍", "dataFetching": "ការទាញយកទិន្នន័យ", "designDocs": "ឯកសាររចនា", "designDocsExplainer": "អានឯកសាររចនា O3", "explainer": "ឧទាហរណ៍ខាងក្រោមបង្ហាញពីលក្ខណៈសំខាន់ៗមួយចំនួននៃក្របខ័ណ្ឌ O3", "extensionExplainer": "នេះគឺជាប្រអប់ពណ៌មួយចំនួន។ ដោយសារតែពួកវាត្រូវបានភ្ជាប់ជាផ្នែកបន្ថែមនៅក្នុងរន្ធដោត អ្នកគ្រប់គ្រងអាចផ្លាស់ប្តូរអ្វីដែលប្រអប់ត្រូវបានបង្ហាញដោយប្រើការកំណត់។ ប្រអប់ទាំងនេះត្រូវបានកំណត់នៅក្នុងម៉ូឌុលនេះ ប៉ុន្តែពួកវាអាចភ្ជាប់ជាមួយរន្ធដោតនេះ ទោះបីជាពួកគេស្ថិតនៅក្នុងម៉ូឌុលផ្សេងក៏ដោយ។", "extensionSystem": "ប្រព័ន្ធបន្ថែម", "formalGreeting": "សួស្តី", "frontendDocs": "Frontend docs", "getPatient": "យកអ្នកជំងឺឈ្មោះ", "getStarted": "ការចាប់ផ្តើម", "getStartedExplainer": "បង្កើតម៉ូឌុលផ្នែកខាងមុខពីគំរូនេះ។", "learnExplainer": "រៀនពីរបៀបប្រើ O3 framework", "loading": "កំពុងផ្ទុក", "patientGetterExplainer": "ព្យាយាមចុចប៊ូតុងខាងក្រោមដើម្បីទាញយកអ្នកជំងឺពី API", "resources": "ធនធាន", "usefulLinks": "ខាងក្រោមនេះគឺជាតំណភ្ជាប់មួយចំនួនទៅកាន់ធនធានដែលមានប្រយោជន៍", "welcomeText": "សូមស្វាគមន៍មកកាន់កម្មវិធីគំរូ O3"}