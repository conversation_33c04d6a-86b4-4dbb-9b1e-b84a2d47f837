{"name": "@openmrs/esm-referral-app", "version": "5.0.5", "license": "MPL-2.0", "description": "An OpenMRS seed application for building microfrontends", "browser": "dist/openmrs-esm-referral-app.js", "main": "src/index.ts", "source": true, "scripts": {"start": "openmrs develop", "serve": "webpack serve --mode=development", "build": "webpack --mode production", "analyze": "webpack --mode=production --env analyze=true", "lint": "eslint src --ext js,jsx,ts,tsx --max-warnings 0", "prettier": "prettier --write \"src/**/*.{ts,tsx}\" --list-different", "typescript": "tsc", "test": "jest --config jest.config.js --passWithNoTests", "verify": "turbo lint typescript coverage", "coverage": "yarn test --coverage", "prepare": "husky install", "extract-translations": "i18next 'src/**/*.component.tsx' --config ./tools/i18next-parser.config.js", "test-e2e": "playwright test"}, "browserslist": ["extends browserslist-config-openmrs"], "keywords": ["openmrs", "microfrontends"], "repository": {"type": "git", "url": "git+https://github.com/openmrs/openmrs-esm-template-app.git"}, "homepage": "https://github.com/openmrs/openmrs-esm-template-app#readme", "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/openmrs/openmrs-esm-template-app/issues"}, "dependencies": {"@carbon/react": "^1.83.0", "fast-xml-parser": "^5.2.5", "lodash-es": "^4.17.21"}, "peerDependencies": {"@openmrs/esm-framework": "*", "@openmrs/esm-patient-common-lib": "*", "dayjs": "1.x", "react": "18.x", "react-i18next": "11.x", "react-router-dom": "6.x", "rxjs": "6.x"}, "devDependencies": {"@openmrs/esm-framework": "next", "@openmrs/esm-patient-common-lib": "next", "@openmrs/esm-styleguide": "next", "@playwright/test": "^1.52.0", "@swc/cli": "^0.3.12", "@swc/core": "^1.3.68", "@swc/jest": "^0.2.36", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.6", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@types/webpack-env": "^1.18.1", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "css-loader": "^6.8.1", "dayjs": "^1.11.13", "dotenv": "^16.0.3", "eslint": "^8.50.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^8.0.3", "i18next": "^23.2.8", "i18next-parser": "^9.0.2", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.2", "openmrs": "next", "prettier": "^3.3.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^11.18.6", "react-router-dom": "^6.14.1", "rxjs": "^6.6.7", "swc-loader": "^0.2.3", "turbo": "^2.5.2", "typescript": "^4.9.5", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "lint-staged": {"packages/**/src/**/*.{ts,tsx}": "eslint --cache --fix --max-warnings 0", "*.{css,scss,ts,tsx}": "prettier --write --list-different"}, "packageManager": "yarn@4.9.1"}