@use '@carbon/layout';
@use '@carbon/type';

.container {
  padding: layout.$spacing-07;
}

.heading {
  @include type.type-style('heading-04');
  margin: layout.$spacing-05 0;
}

.explainer {
  display: block;
}

.resources {
  margin-top: layout.$spacing-10;
  margin-bottom: layout.$spacing-09;

  > * + * {
    margin-right: layout.$spacing-05;
  }
}

.cardsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  column-gap: layout.$spacing-06;
  margin-top: layout.$spacing-07;
  max-width: 75%;
}

.card {
  margin: layout.$spacing-03 0;
  display: flex;
  align-items: center;

  &:hover {
    border: 1px solid lightgray;
  }

  svg {
    margin-left: layout.$spacing-02;
  }
}

.cardLink {
  text-decoration: none;
  color: inherit;
  display: block;
}

.cardContent {
  display: flex;
  flex-direction: column;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: layout.$spacing-05;

  h4 {
    @include type.type-style('heading-02');
  }
}

