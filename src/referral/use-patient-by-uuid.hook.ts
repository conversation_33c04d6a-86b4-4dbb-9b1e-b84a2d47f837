import useS<PERSON> from 'swr';
import { fhirBaseUrl, openmrsFetch } from '@openmrs/esm-framework';

/**
 * Hook to fetch a patient by UUID from the OpenMRS FHIR API
 * @param patientUuid The UUID of the patient to fetch
 * @returns Patient data, loading state, and error state
 */
export function usePatientByUuid(patientUuid: string | null) {
  const url = patientUuid ? `${fhirBaseUrl}/Patient/${patientUuid}` : null;
  
  const { data, error, isLoading } = useSWR<
    {
      data: fhir.Patient;
    },
    Error
  >(url, openmrsFetch);

  return {
    patient: data?.data || null,
    error: error,
    isLoading,
  };
} 