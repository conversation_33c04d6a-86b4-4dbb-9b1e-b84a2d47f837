import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MenuItem } from '@carbon/react';
import { Send } from '@carbon/react/icons';
import ReferralModal from './referral-modal.component';

export default function ReferPatientButton() {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <MenuItem
        label={t('referPatient', 'Refer Patient')}
        onClick={handleClick}
      >
      
      </MenuItem>
      {isModalOpen && (
        <ReferralModal 
          isOpen={isModalOpen} 
          onClose={handleCloseModal}
        />
      )}
    </>
  );
} 