import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  TextInput,
  TextArea,
  Select,
  SelectItem,
  Stack,
} from '@carbon/react';
import styles from './referral-modal.scss';
import ReferralLetterModal from './referral-letter-modal.component';

interface ReferralModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ReferralModal: React.FC<ReferralModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const [isLetterModalOpen, setIsLetterModalOpen] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    hospitalName: '',
    hospitalAddress: '',
    urgencyLevel: '',
    reasonForReferral: '',
    conditionSummary: '',
    typeOfReferral: '',
    referralSummary: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-populate referral summary based on type of referral
    if (field === 'typeOfReferral') {
      const defaultSummary = getDefaultReferralSummary(value);
      setFormData(prev => ({ ...prev, referralSummary: defaultSummary }));
    }
  };

  const getDefaultReferralSummary = (typeOfReferral: string): string => {
    switch (typeOfReferral) {
      case 'specialist':
        return 'I request specialist consultation for this patient to evaluate their condition and provide expert medical opinion.';

      case 'diagnostic':
        return 'I request diagnostic evaluation and testing for this patient to determine the underlying cause of their symptoms.';

      case 'emergency':
        return 'I request emergency treatment and immediate evaluation for this patient due to urgent medical concerns.';

      case 'surgical':
        return 'I request surgical consultation for this patient to evaluate surgical options and determine the best course of treatment.';

      case 'mental-health':
        return 'I request mental health evaluation and assessment for this patient to address their psychological and behavioral health needs.';

      case 'rehabilitation':
        return 'I request rehabilitation services for this patient to improve their functional abilities and quality of life.';

      case 'second-opinion':
        return 'I request a second opinion consultation for this patient to review their current diagnosis and treatment plan.';

      case 'therapeutic':
        return 'I request therapeutic intervention for this patient to address their specific medical condition and treatment needs.';

      default:
        return '';
    }
  };

  const handleProceed = () => {
    // Hide details modal and show letter modal
    setShowDetailsModal(false);
    setIsLetterModalOpen(true);
  };

  const handleLetterModalClose = () => {
    setIsLetterModalOpen(false);
    setShowDetailsModal(true);
    onClose();
  };

  const handleDetailsModalClose = () => {
    setShowDetailsModal(true);
    setIsLetterModalOpen(false);
    onClose();
  };

  return (
    <>
      <Modal
        open={isOpen && showDetailsModal}
        onRequestClose={handleDetailsModalClose}
        modalHeading="Referral Details"
        primaryButtonText="Proceed"
        secondaryButtonText="Close"
        onRequestSubmit={handleProceed}
        className={styles.referralModal}
      >
        <div className={styles.modalContent}>
          <p className={styles.description}>
            Provide the details below before you can refer this patient to another doctor.
          </p>

          <div className={styles.section}>
            <h4 className={styles.sectionTitle}>Recipient Information</h4>
            
            <Stack gap={5}>
              <TextInput
                id="name"
                labelText="Doctor Name"
                placeholder="Type here..."
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
              />

              <TextInput
                id="hospitalName"
                labelText="Hospital Name"
                placeholder="Type here..."
                value={formData.hospitalName}
                onChange={(e) => handleInputChange('hospitalName', e.target.value)}
              />

              <TextInput
                id="hospitalAddress"
                labelText="Hospital Address"
                placeholder="Type here..."
                value={formData.hospitalAddress}
                onChange={(e) => handleInputChange('hospitalAddress', e.target.value)}
              />
            </Stack>
          </div>

          <div className={styles.section}>
            <h4 className={styles.sectionTitle}>Other Information</h4>
            
            <Stack gap={5}>
              <Select
                id="urgencyLevel"
                labelText="Urgency Level"
                value={formData.urgencyLevel}
                onChange={(e) => handleInputChange('urgencyLevel', e.target.value)}
              >
                <SelectItem value="" text="Select" />
                <SelectItem value="low" text="Low" />
                <SelectItem value="medium" text="Medium" />
                <SelectItem value="high" text="High" />
                <SelectItem value="urgent" text="Urgent" />
              </Select>

              <Select
                id="typeOfReferral"
                labelText="Type of Referral"
                value={formData.typeOfReferral}
                onChange={(e) => handleInputChange('typeOfReferral', e.target.value)}
              >
                <SelectItem value="" text="Select" />
                <SelectItem value="specialist" text="Specialist Referrals" />
                <SelectItem value="diagnostic" text="Diagnostic Referrals" />
                <SelectItem value="therapeutic" text="Therapeutic Referrals" />
                <SelectItem value="emergency" text="Emergency Referrals" />
                <SelectItem value="second-opinion" text="Second Opinion Referrals" />
                <SelectItem value="surgical" text="Surgical Referrals" />
                <SelectItem value="mental-health" text="Mental Health Referrals" />
                <SelectItem value="rehabilitation" text="Rehabilitation Referrals" />
              </Select>

              <TextArea
                id="reasonForReferral"
                labelText="Reason for referral"
                placeholder="Type here..."
                value={formData.reasonForReferral}
                onChange={(e) => handleInputChange('reasonForReferral', e.target.value)}
                rows={3}
              />

              <TextArea
                id="conditionSummary"
                labelText="Condition/Symptoms Summary"
                placeholder="Type here..."
                value={formData.conditionSummary}
                onChange={(e) => handleInputChange('conditionSummary', e.target.value)}
                rows={3}
              />

              <TextArea
                id="referralSummary"
                labelText="Referral Summary"
                placeholder="Type here..."
                value={formData.referralSummary}
                onChange={(e) => handleInputChange('referralSummary', e.target.value)}
                rows={3}
              />
            </Stack>
          </div>
        </div>
      </Modal>
      
      <ReferralLetterModal
        isOpen={isLetterModalOpen}
        onClose={handleLetterModalClose}
        referralData={formData}
      />
    </>
  );
};

export default ReferralModal; 