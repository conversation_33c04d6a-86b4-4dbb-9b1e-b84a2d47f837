import { useEffect, useState } from 'react';

/**
 * Hook to extract the current patient UUID from the URL
 * Works with OpenMRS patient chart URL patterns like:
 * - /openmrs/spa/patient/{patientUuid}/chart
 * - /patient/{patientUuid}
 * - Any URL that contains a patient UUID segment
 */
export function useCurrentPatientUuid(): string | null {
  const [patientUuid, setPatientUuid] = useState<string | null>(null);

  useEffect(() => {
    const extractPatientUuid = () => {
      const pathname = window.location.pathname;
      
      // Try to find patient UUID in different URL patterns
      const pathSegments = pathname.split('/').filter(segment => segment.length > 0);
      
      // Pattern 1: /openmrs/spa/patient/{uuid}/...
      const patientIndex = pathSegments.findIndex(segment => segment === 'patient');
      if (patientIndex !== -1 && pathSegments[patientIndex + 1]) {
        const uuid = pathSegments[patientIndex + 1];
        // Basic UUID validation (36 characters with hyphens)
        if (uuid.length === 36 && uuid.includes('-')) {
          return uuid;
        }
      }

      // Pattern 2: Try to find any segment that looks like a UUID
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      for (const segment of pathSegments) {
        if (uuidPattern.test(segment)) {
          return segment;
        }
      }

      return null;
    };

    const uuid = extractPatientUuid();
    setPatientUuid(uuid);

    // Listen for URL changes
    const handleLocationChange = () => {
      const newUuid = extractPatientUuid();
      setPatientUuid(newUuid);
    };

    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', handleLocationChange);
    
    return () => {
      window.removeEventListener('popstate', handleLocationChange);
    };
  }, []);

  return patientUuid;
} 