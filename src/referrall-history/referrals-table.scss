.container {
  padding: 1rem;
  max-width: 100%;
  overflow-x: auto;
}

.header {
  margin-bottom: 1.5rem;
  
  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #161616;
  }
}

.subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: #525252;
}

.tableContainer {
  min-height: 300px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.errorTile {
  padding: 2rem;
  text-align: center;
  border: 1px solid #da1e28;
  background-color: #fff1f1;
  
  h4 {
    margin: 0 0 1rem 0;
    color: #da1e28;
    font-size: 1.125rem;
    font-weight: 600;
  }
  
  p {
    margin: 0 0 0.5rem 0;
    color: #161616;
  }
}

.errorDetails {
  font-size: 0.875rem;
  color: #6f6f6f;
  font-style: italic;
}

.emptyState {
  padding: 3rem 2rem;
  text-align: center;
  background-color: #f4f4f4;
  
  h4 {
    margin: 0 0 1rem 0;
    color: #161616;
    font-size: 1.125rem;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    color: #525252;
  }
}

// Loading state styling
.container :global(.cds--inline-loading) {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

// Table responsive behavior
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }
  
  .tableContainer {
    font-size: 0.875rem;
  }
}

// Table header styling
.tableContainer :global(.cds--data-table-header) {
  background-color: #f4f4f4;
  font-weight: 600;
}

// Table row hover effect
.tableContainer :global(.cds--data-table tbody tr:hover) {
  background-color: #f4f4f4;
}

// Table cell padding
.tableContainer :global(.cds--data-table td),
.tableContainer :global(.cds--data-table th) {
  padding: 0.75rem 1rem;
} 