{"$schema": "https://json.openmrs.org/routes.schema.json", "backendDependencies": {"fhir2": ">=1.2", "webservices.rest": "^2.24.0"}, "extensions": [{"name": "Red box", "component": "redBox", "slot": "Boxes"}, {"name": "Blue box", "component": "blueBox", "slot": "Boxes"}, {"name": "Brand box", "component": "blueBox", "slot": "Boxes"}, {"name": "Refer <PERSON><PERSON>", "component": "referPatientButton", "slot": "patient-actions-slot"}, {"name": "test-dashboard-component", "component": "testDashboardshow", "slot": "test-dashboard-slot", "online": true, "offline": true}, {"name": "test-dashboard", "component": "testDashboardLink", "slot": "patient-chart-dashboard-slot", "meta": {"slot": "test-dashboard-slot", "path": "test"}}], "pages": [{"component": "root", "route": "root"}, {"component": "referralFormWorkspace", "route": "referral-form"}]}