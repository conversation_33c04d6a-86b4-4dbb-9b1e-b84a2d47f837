/* General features of extension styling are controlled
 * at the slot level. The boxes should know as little as
 * possible about the context where they are used, so
 * they do not set their own sizes.
 *
 * `> * > *` is used to target the outermost DOM node of
 * the extension.
 */
 @use '@carbon/layout';

.box > * > * {
  height: layout.$spacing-10;
  width: layout.$spacing-10;
}

.boxes {
  margin: layout.$spacing-07 0;
  display: flex;
  gap: layout.$spacing-05;
}

.container {
  max-width: 50rem;
  margin-top: layout.$spacing-09;

  > * + * {
    margin-top: layout.$spacing-06;
  }
}
