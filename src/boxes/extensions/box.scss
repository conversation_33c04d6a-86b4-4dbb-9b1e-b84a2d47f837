/* Extensions should supply the minimum amount of styling
 * necessary. Here, the extensions set only their colors.
 * Their sizes and other general features of their display
 * is controlled by the slot. */
@use "@carbon/layout";
@use '@openmrs/esm-styleguide/src/vars' as *;

 .blue {
  background-color: darkblue;
}

.red {
  background-color: darkred;
}

/* Brand colors are special. They must be included using a
 * SASS mix-in (shown here) or using a CSS variable like
 * `var(--brand-01)`. */
.brand {
  @include brand-01(background-color);
  color: white;
  padding: layout.$spacing-03;
}
